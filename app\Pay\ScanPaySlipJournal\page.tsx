"use client";
import React, { useState, useCallback, useEffect, Fragment } from "react";
import { getAuth, onAuthStateChanged } from "firebase/auth";
import { useRouter } from "next/navigation";
import Sidebar from "../../DashBoard/SideBar";
import { fetchUserClients } from "../../../utils/accountUtils";
import {
  CloudArrowUpIcon,
  DocumentDuplicateIcon,
  XMarkIcon,
  ArrowDownTrayIcon,
  TableCellsIcon,
  CheckCircleIcon,
  UserGroupIcon,
  DocumentTextIcon,
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CurrencyDollarIcon,
  CalculatorIcon,
  ClipboardDocumentListIcon,
  SparklesIcon
} from "@heroicons/react/24/outline";
import { GoogleGenAI } from "@google/genai";
import { useDropzone } from "react-dropzone";
import * as XLSX from 'xlsx';
import { Transition, Dialog } from '@headlessui/react';
import { storage, ref, uploadBytes, getDownloadURL } from "../../../firebase";
import { getEffectiveUserId } from "../../../utils/accountUtils";

// API Keys
const API_KEY3 = "AIzaSyA1b9WCqkH5KHkc5SCAS_3XAC7XtB97QoM"; // FREE TRIAL API KEY

// Interfaces
interface JournalEntry {
  journal: string;
  date: string;
  account: string;
  label: string;
  debit?: number;
  credit?: number;
  employeeName?: string;
}

interface PayrollData {
  month: string;
  year: string;
  employer: string;
  employees: EmployeePayrollData[];
}

interface EmployeePayrollData {
  name: string;
  grossSalary: number;
  cnssEmployee: number;
  cnssEmployer: number;
  tfp: number;
  foprolos: number;
  irpp: number;
  salaryAdvance: number;
  netSalary: number;
}

interface FileUpload {
  file: File;
  id: string;
  status: 'uploading' | 'storage-uploading' | 'processing' | 'completed' | 'error';
  progress: number;
  storageProgress: number;
  result?: PayrollData;
  error?: string;
  storageUrl?: string;
  storagePath?: string;
  storageError?: string;
}

interface Client {
  id: string;
  name: string;
  employeesNumber?: string;
  [key: string]: any;
}

const ScanPaySlipJournal: React.FC = () => {
  // State management
  const [user, setUser] = useState<any>(null);
  const [clients, setClients] = useState<Record<string, Client>>({});
  const [selectedClientId, setSelectedClientId] = useState<string>("");
  const [selectedClient, setSelectedClient] = useState<Client | null>(null);
  const [files, setFiles] = useState<FileUpload[]>([]);
  const [journalEntries, setJournalEntries] = useState<JournalEntry[]>([]);
  const [showJournalModal, setShowJournalModal] = useState(false);
  const [selectedMonth, setSelectedMonth] = useState<number>(new Date().getMonth() + 1);
  const [selectedYear, setSelectedYear] = useState<number>(new Date().getFullYear());
  const [employerName, setEmployerName] = useState<string>("");
  const [showValidationModal, setShowValidationModal] = useState(false);
  const [validationData, setValidationData] = useState<{
    uploadedCount: number;
    expectedCount: number;
    clientName: string;
  } | null>(null);
  const [pendingFiles, setPendingFiles] = useState<FileUpload[]>([]);
  const [isGeneratingJournal, setIsGeneratingJournal] = useState(false);

  const router = useRouter();

  // Authentication effect
  useEffect(() => {
    const auth = getAuth();
    const unsubscribe = onAuthStateChanged(auth, async (user) => {
      if (user) {
        setUser(user);
        try {
          const clientsData = await fetchUserClients(user);
          console.log('Fetched clients:', clientsData);
          setClients(clientsData);
        } catch (error) {
          console.error('Error fetching clients:', error);
        }
      } else {
        router.push('/SignInScreen');
      }
    });

    return () => unsubscribe();
  }, [router]);

  // Client selection effect
  useEffect(() => {
    if (selectedClientId && clients[selectedClientId]) {
      setSelectedClient(clients[selectedClientId]);
      setEmployerName(clients[selectedClientId].name || "");
    }
  }, [selectedClientId, clients]);

  // Payroll Journal Processing Prompt - Your specific prompt
  const generatePayrollJournalPrompt = () => {
    return `# Payroll Journal Processing Instructions

You are tasked with extracting payroll data and creating appropriate journal entries. Follow these steps precisely:

## Context
- Source: Payroll data containing employee salary information
- Target: Multiple journal entries in different journals (BANQUE and SALAIRE)
- Month: ${selectedMonth}/${selectedYear}

## Processing Steps

### Step 1: Individual Salary Advances
**Action**: Extract individual salary advances
- **Source Field**: AVANCE SUR SALAIRE (per employee)
- **Target Journal**: BANQUE
- **Account**: PERSONNEL AVANCE ET ACCOMPTE
- **Label Format**: "REGLEMENT DE LAVANCE SUR SALAIRE / [EMPLOYEE_NAME]"
- **Entry Type**: DEBIT
- **Processing**: Create one entry per employee with advance

### Step 2: Gross Salaries Total
**Action**: Calculate and post total gross salaries
- **Source Field**: SALAIRES BRUTS (all employees)
- **Calculation**: SUM(all employee gross salaries)
- **Target Journal**: SALAIRE
- **Account**: SALAIRE ET COMPLEMENT DE SALAIRE
- **Label Format**: "SALAIRE MOIS DE ${selectedMonth}/${selectedYear}"
- **Entry Type**: DEBIT

### Step 3: Employer CNSS Contributions
**Action**: Calculate and post total employer CNSS
- **Source Field**: CNSS PATRONALES (all employees)
- **Calculation**: SUM(all employer CNSS contributions)
- **Target Journal**: SALAIRE
- **Account**: CHARGE SOCIALE LEGALE
- **Label Format**: "SALAIRE MOIS DE ${selectedMonth}/${selectedYear}"
- **Entry Type**: DEBIT

### Step 4: TFP Total
**Action**: Calculate and post total TFP
- **Source Field**: TFP (all employees)
- **Calculation**: SUM(all TFP amounts)
- **Target Journal**: SALAIRE
- **Account**: TFP
- **Label Format**: "SALAIRE MOIS DE ${selectedMonth}/${selectedYear}"
- **Entry Type**: DEBIT

### Step 5: FOPROLOS Total
**Action**: Calculate and post total FOPROLOS
- **Source Field**: FOPROLOS (all employees)
- **Calculation**: SUM(all FOPROLOS amounts)
- **Target Journal**: SALAIRE
- **Account**: FOPROLOS
- **Label Format**: "SALAIRE MOIS DE ${selectedMonth}/${selectedYear}"
- **Entry Type**: DEBIT

### Step 6: IRPP Withholding
**Action**: Calculate and post total IRPP
- **Source Field**: IRPP (all employees)
- **Calculation**: SUM(all IRPP withholdings)
- **Target Journal**: SALAIRE
- **Account**: RETENUE A LA SOURCE SUR SALAIRE
- **Label Format**: "SALAIRE MOIS DE ${selectedMonth}/${selectedYear}"
- **Entry Type**: CREDIT

### Step 7: Salary Advances Total
**Action**: Calculate and post total advances
- **Source Field**: AVANCE SUR SALAIRE (all employees)
- **Calculation**: SUM(all salary advances)
- **Target Journal**: SALAIRE
- **Account**: PERSONNEL AVANCE ET ACCOMPTE
- **Label Format**: "SALAIRE MOIS DE ${selectedMonth}/${selectedYear}"
- **Entry Type**: CREDIT

### Step 8: Total CNSS (Employee + Employer)
**Action**: Calculate and post combined CNSS
- **Source Fields**: COTISATION CNSS + CNSS PATRONALES (all employees)
- **Calculation**: SUM(all employee CNSS) + SUM(all employer CNSS)
- **Target Journal**: SALAIRE
- **Account**: CNSS
- **Label Format**: "SALAIRE MOIS DE ${selectedMonth}/${selectedYear}"
- **Entry Type**: CREDIT

### Step 9: Total Other Taxes (TFP + FOPROLOS)
**Action**: Calculate and post combined taxes
- **Source Fields**: TFP + FOPROLOS (all employees)
- **Calculation**: SUM(all TFP) + SUM(all FOPROLOS)
- **Target Journal**: SALAIRE
- **Account**: AUTRES IMPORT TAXES
- **Label Format**: "SALAIRE MOIS DE ${selectedMonth}/${selectedYear}"
- **Entry Type**: CREDIT

## Expected Output Format
Return the data as a JSON object with the following structure:
{
  "journalEntries": [
    {
      "journal": "BANQUE" or "SALAIRE",
      "date": "${selectedYear}-${selectedMonth.toString().padStart(2, '0')}-01",
      "account": "Account Name",
      "label": "Description",
      "debit": amount (if debit entry),
      "credit": amount (if credit entry),
      "employeeName": "Employee Name" (for individual entries)
    }
  ]
}

## Validation Rules
1. Ensure all amounts are positive values
2. Verify that total debits equal total credits for the SALAIRE journal
3. Replace [EMPLOYEE_NAME] with actual employee names for individual entries
4. All amounts should be in numeric format without currency symbols`;
  };

  // Process payroll data with Gemini AI
  const processPayrollWithGemini = async (file: File): Promise<PayrollData | null> => {
    try {
      const base64String = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = () => {
          const result = reader.result as string;
          const base64 = result.split(',')[1];
          resolve(base64);
        };
        reader.readAsDataURL(file);
      });

      const ai = new GoogleGenAI({ apiKey: API_KEY3 });

      const response = await ai.models.generateContent({
        model: "gemini-2.0-flash-exp",
        contents: [
          {
            role: "user",
            parts: [
              { text: "Please analyze this payroll document and extract the payroll information to create journal entries according to the instructions." },
              {
                inlineData: {
                  mimeType: file.type,
                  data: base64String
                }
              }
            ]
          }
        ],
        config: {
          systemInstruction: generatePayrollJournalPrompt(),
          temperature: 0.2,
          topP: 0.95,
          topK: 64,
          maxOutputTokens: 65536,
        }
      });

      const responseText = response.text || "";
      return extractPayrollData(responseText);
    } catch (error) {
      console.error('Gemini processing error:', error);
      return null;
    }
  };

  // Extract payroll data from AI response
  const extractPayrollData = (responseText: string): PayrollData | null => {
    try {
      console.log('Raw AI response:', responseText);

      // Try multiple approaches to extract JSON
      let jsonData = null;

      // Method 1: Look for JSON between ```json and ``` markers
      const jsonCodeBlockMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonCodeBlockMatch) {
        try {
          jsonData = JSON.parse(jsonCodeBlockMatch[1].trim());
        } catch (e) {
          console.log('Failed to parse JSON from code block:', e);
        }
      }

      // Method 2: Look for JSON between { and } (find the largest valid JSON)
      if (!jsonData) {
        const jsonMatches = responseText.match(/\{[\s\S]*\}/g);
        if (jsonMatches) {
          // Try each match, starting with the longest
          const sortedMatches = jsonMatches.sort((a, b) => b.length - a.length);
          for (const match of sortedMatches) {
            try {
              jsonData = JSON.parse(match);
              break; // Success, use this one
            } catch (e) {
              console.log('Failed to parse JSON match:', e);
              continue;
            }
          }
        }
      }

      // Method 3: Try to clean and parse the entire response
      if (!jsonData) {
        try {
          // Remove markdown formatting and extra text
          let cleanedResponse = responseText
            .replace(/```json/g, '')
            .replace(/```/g, '')
            .replace(/^[^{]*/, '') // Remove text before first {
            .replace(/[^}]*$/, '') // Remove text after last }
            .trim();

          jsonData = JSON.parse(cleanedResponse);
        } catch (e) {
          console.log('Failed to parse cleaned response:', e);
        }
      }

      if (!jsonData) {
        console.error('No valid JSON found in response');
        return null;
      }

      console.log('Parsed JSON data:', jsonData);

      // Convert the AI response to our PayrollData format
      if (jsonData.journalEntries && Array.isArray(jsonData.journalEntries)) {
        // Extract employee data from journal entries
        const employees: EmployeePayrollData[] = [];
        const employeeMap = new Map<string, EmployeePayrollData>();

        jsonData.journalEntries.forEach((entry: any) => {
          if (entry.employeeName && entry.employeeName !== "Grand Total") {
            if (!employeeMap.has(entry.employeeName)) {
              employeeMap.set(entry.employeeName, {
                name: entry.employeeName,
                grossSalary: 0,
                cnssEmployee: 0,
                cnssEmployer: 0,
                tfp: 0,
                foprolos: 0,
                irpp: 0,
                salaryAdvance: 0,
                netSalary: 0
              });
            }
          }
        });

        return {
          month: selectedMonth.toString(),
          year: selectedYear.toString(),
          employer: employerName,
          employees: Array.from(employeeMap.values())
        };
      }

      return null;
    } catch (error) {
      console.error('Error parsing AI response:', error);
      return null;
    }
  };

  // Generate journal entries from processed payroll data
  const generateJournalEntries = async () => {
    setIsGeneratingJournal(true);
    try {
      const completedFiles = files.filter(f => f.status === 'completed' && f.result);
      if (completedFiles.length === 0) {
        alert('No processed payroll files available to generate journal entries');
        return;
      }

      // Combine all payroll data
      const allEmployees: EmployeePayrollData[] = [];
      completedFiles.forEach(file => {
        if (file.result?.employees) {
          allEmployees.push(...file.result.employees);
        }
      });

      // Use Gemini to generate journal entries based on the combined data
      const ai = new GoogleGenAI({ apiKey: API_KEY3 });

      const payrollSummary = {
        month: selectedMonth,
        year: selectedYear,
        employer: employerName,
        employees: allEmployees
      };

      const response = await ai.models.generateContent({
        model: "gemini-2.0-flash-exp",
        contents: [
          {
            role: "user",
            parts: [
              {
                text: `Please create journal entries for this payroll data according to the instructions: ${JSON.stringify(payrollSummary)}`
              }
            ]
          }
        ],
        config: {
          systemInstruction: generatePayrollJournalPrompt(),
          temperature: 0.2,
          topP: 0.95,
          topK: 64,
          maxOutputTokens: 65536,
        }
      });

      const responseText = response.text || "";
      console.log('Journal generation response:', responseText);

      // Use the same improved JSON parsing logic
      let jsonData = null;

      // Method 1: Look for JSON between ```json and ``` markers
      const jsonCodeBlockMatch = responseText.match(/```json\s*([\s\S]*?)\s*```/);
      if (jsonCodeBlockMatch) {
        try {
          jsonData = JSON.parse(jsonCodeBlockMatch[1].trim());
        } catch (e) {
          console.log('Failed to parse JSON from code block:', e);
        }
      }

      // Method 2: Look for JSON between { and } (find the largest valid JSON)
      if (!jsonData) {
        const jsonMatches = responseText.match(/\{[\s\S]*\}/g);
        if (jsonMatches) {
          // Try each match, starting with the longest
          const sortedMatches = jsonMatches.sort((a, b) => b.length - a.length);
          for (const match of sortedMatches) {
            try {
              jsonData = JSON.parse(match);
              break; // Success, use this one
            } catch (e) {
              console.log('Failed to parse JSON match:', e);
              continue;
            }
          }
        }
      }

      // Method 3: Try to clean and parse the entire response
      if (!jsonData) {
        try {
          // Remove markdown formatting and extra text
          let cleanedResponse = responseText
            .replace(/```json/g, '')
            .replace(/```/g, '')
            .replace(/^[^{]*/, '') // Remove text before first {
            .replace(/[^}]*$/, '') // Remove text after last }
            .trim();

          jsonData = JSON.parse(cleanedResponse);
        } catch (e) {
          console.log('Failed to parse cleaned response:', e);
        }
      }

      if (jsonData && jsonData.journalEntries && Array.isArray(jsonData.journalEntries)) {
        console.log('Successfully parsed journal entries:', jsonData.journalEntries);
        setJournalEntries(jsonData.journalEntries);
        setShowJournalModal(true);
      } else {
        console.error('No valid journal entries found in response');
        alert('Failed to parse journal entries from AI response');
      }
    } catch (error) {
      console.error('Error generating journal entries:', error);
      alert('Failed to generate journal entries');
    } finally {
      setIsGeneratingJournal(false);
    }
  };

  // File upload handlers
  const onDrop = useCallback((acceptedFiles: File[]) => {
    const newFiles: FileUpload[] = acceptedFiles.map(file => ({
      file,
      id: Math.random().toString(36).substring(2, 11),
      status: 'uploading',
      progress: 0,
      storageProgress: 0
    }));

    // Validate payroll file count before processing
    if (validateFileCount(newFiles)) {
      // If validation passes, add files and process them
      setFiles(prev => [...prev, ...newFiles]);
      newFiles.forEach(fileUpload => {
        processFile(fileUpload);
      });
    }
    // If validation fails, the modal will be shown and files will be held in pendingFiles
  }, [files.length, selectedClient]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'image/*': ['.png', '.jpg', '.jpeg']
    },
    multiple: true
  });

  // Process individual file
  const processFile = async (fileUpload: FileUpload) => {
    try {
      // Step 1: Upload to Firebase Storage first
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, status: 'storage-uploading', progress: 0 }
          : f
      ));

      const storageResult = await uploadToFirebaseStorage(fileUpload);

      if (storageResult) {
        // Update with storage success
        setFiles(prev => prev.map(f =>
          f.id === fileUpload.id
            ? {
                ...f,
                storageUrl: storageResult.url,
                storagePath: storageResult.path,
                status: 'processing',
                progress: 25
              }
            : f
        ));
      } else {
        // Storage failed, but continue with AI processing (with warning)
        setFiles(prev => prev.map(f =>
          f.id === fileUpload.id
            ? {
                ...f,
                status: 'processing',
                progress: 25,
                storageError: 'Storage upload failed, but continuing with processing'
              }
            : f
        ));
      }

      // Step 2: Process with Gemini AI
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, progress: 50 }
          : f
      ));

      const result = await processPayrollWithGemini(fileUpload.file);

      if (result) {
        // Update with AI processing result
        setFiles(prev => prev.map(f =>
          f.id === fileUpload.id
            ? { ...f, status: 'completed', progress: 100, result }
            : f
        ));
      } else {
        // Update with AI processing error
        setFiles(prev => prev.map(f =>
          f.id === fileUpload.id
            ? { ...f, status: 'error', progress: 0, error: 'Failed to process payroll data with AI' }
            : f
        ));
      }
    } catch (error) {
      console.error('Error processing file:', error);
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, status: 'error', progress: 0, error: 'Processing failed' }
          : f
      ));
    }
  };

  // Remove file
  const removeFile = (fileId: string) => {
    setFiles(prev => prev.filter(f => f.id !== fileId));
  };

  // Validate file count against expected employee count
  const validateFileCount = (newFiles: FileUpload[]): boolean => {
    if (!selectedClient || !selectedClient.employeesNumber) {
      // If no client selected or no employee count available, allow upload
      return true;
    }

    const expectedCount = parseInt(selectedClient.employeesNumber, 10);
    if (isNaN(expectedCount) || expectedCount <= 0) {
      // If employee count is invalid, allow upload
      return true;
    }

    const totalUploadedCount = files.length + newFiles.length;

    if (totalUploadedCount < expectedCount) {
      // Show validation modal
      setValidationData({
        uploadedCount: totalUploadedCount,
        expectedCount: expectedCount,
        clientName: selectedClient.name
      });
      setPendingFiles(newFiles);
      setShowValidationModal(true);
      return false;
    }

    return true;
  };

  // Handle validation modal actions
  const handleContinueAnyway = () => {
    setShowValidationModal(false);
    if (pendingFiles.length > 0) {
      // Add pending files to the main files list and process them
      setFiles(prev => [...prev, ...pendingFiles]);
      pendingFiles.forEach(fileUpload => {
        processFile(fileUpload);
      });
      setPendingFiles([]);
    }
    setValidationData(null);
  };

  const handleCancelUpload = () => {
    setShowValidationModal(false);
    setPendingFiles([]);
    setValidationData(null);
  };

  // Upload file to Firebase Storage
  const uploadToFirebaseStorage = async (fileUpload: FileUpload): Promise<{url: string, path: string} | null> => {
    try {
      if (!user || !selectedClientId) {
        throw new Error("User not authenticated or no client selected");
      }

      // Get the effective user ID (parent ID for sub-accounts, own ID for regular users)
      const effectiveUserId = await getEffectiveUserId(user.uid);

      // Create structured path: /payroll-journal/{clientId}/{year}/{month}/
      const year = selectedYear.toString();
      const month = selectedMonth.toString().padStart(2, '0');

      // Generate unique filename to prevent conflicts
      const timestamp = new Date().getTime();
      const fileExtension = fileUpload.file.name.split('.').pop() || '';
      const sanitizedFileName = fileUpload.file.name
        .replace(/[^a-zA-Z0-9.-]/g, '_')
        .replace(/_+/g, '_');

      const fileName = `payroll-journal/${selectedClientId}/${year}/${month}/${timestamp}_${sanitizedFileName}`;

      // Create storage reference
      const storageRef = ref(storage, fileName);

      // Simulate progress updates during upload
      const progressInterval = setInterval(() => {
        setFiles(prev => prev.map(f =>
          f.id === fileUpload.id
            ? { ...f, storageProgress: Math.min(f.storageProgress + 10, 90) }
            : f
        ));
      }, 200);

      // Upload file to Firebase Storage
      const snapshot = await uploadBytes(storageRef, fileUpload.file);
      clearInterval(progressInterval);

      // Update progress to 100%
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, storageProgress: 100 }
          : f
      ));

      // Get download URL
      const downloadUrl = await getDownloadURL(snapshot.ref);

      return {
        url: downloadUrl,
        path: fileName
      };
    } catch (error) {
      console.error('Firebase Storage upload error:', error);
      setFiles(prev => prev.map(f =>
        f.id === fileUpload.id
          ? { ...f, storageError: 'Storage upload failed' }
          : f
      ));
      return null;
    }
  };

  // Export to Excel
  const exportToExcel = () => {
    if (journalEntries.length === 0) {
      alert('No journal entries to export');
      return;
    }

    const excelData = [
      // Header rows
      ['PAYROLL JOURNAL ENTRIES'],
      [''],
      [`MONTH: ${selectedMonth}/${selectedYear}`],
      [`EMPLOYER: ${employerName}`],
      [''],
      // Column headers
      [
        'Journal',
        'Date',
        'Account',
        'Label',
        'Debit',
        'Credit',
        'Employee Name'
      ],
      // Data rows
      ...journalEntries.map(entry => [
        entry.journal,
        entry.date,
        entry.account,
        entry.label,
        entry.debit || '',
        entry.credit || '',
        entry.employeeName || ''
      ])
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(excelData);
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Journal Entries');

    const fileName = `Payroll_Journal_Entries_${selectedMonth}_${selectedYear}_${employerName.replace(/\s+/g, '_')}.xlsx`;
    XLSX.writeFile(workbook, fileName);
  };

  // Format number for display
  const formatNumber = (num: number): string => {
    return new Intl.NumberFormat('en-US', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(num);
  };

  if (!user) {
    return <div>Loading...</div>;
  }

  return (
    <div className="h-screen bg-gray-50 flex flex-col">
      {/* Header - Full Width */}
      <div className="bg-white shadow-sm border-b border-gray-200 z-10">
        <div className="flex">
          {/* Spacer for sidebar width */}
          <div className="w-72 flex-shrink-0"></div>

          {/* Header content */}
          <div className="flex-1 px-6 py-4">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 flex items-center">
                  <ClipboardDocumentListIcon className="h-8 w-8 mr-3 text-blue-600" />
                  Payroll Journal Generator
                </h1>
                <p className="text-gray-600 mt-1">
                  AI-powered payroll processing with automatic journal entry generation
                </p>
              </div>
              <div className="flex items-center space-x-2">
                <SparklesIcon className="h-6 w-6 text-yellow-500" />
                <span className="text-sm text-gray-600">Powered by Gemini AI</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Area with Sidebar */}
      <div className="flex flex-1 overflow-hidden">
        <Sidebar />

        <div className="flex-1 flex flex-col overflow-hidden">
          {/* Main Content */}
          <div className="flex-1 overflow-auto p-6">
            {/* Client Selection */}
            <div className="bg-white rounded-xl shadow-lg border border-gray-200 mb-6 p-6">
              <h2 className="text-lg font-semibold text-gray-800 mb-4 flex items-center">
                <UserGroupIcon className="h-5 w-5 mr-2 text-blue-600" />
                Client & Period Selection
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Client
                  </label>
                  <select
                    value={selectedClientId}
                    onChange={(e) => setSelectedClientId(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="">Select a client</option>
                    {Object.entries(clients).map(([id, client]) => (
                      <option key={id} value={id}>
                        {client.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Month
                  </label>
                  <select
                    value={selectedMonth}
                    onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Array.from({ length: 12 }, (_, i) => {
                      const monthNames = [
                        'January', 'February', 'March', 'April', 'May', 'June',
                        'July', 'August', 'September', 'October', 'November', 'December'
                      ];
                      return (
                        <option key={i + 1} value={i + 1}>
                          {monthNames[i]}
                        </option>
                      );
                    })}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Year
                  </label>
                  <select
                    value={selectedYear}
                    onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  >
                    {Array.from({ length: 5 }, (_, i) => {
                      const year = new Date().getFullYear() - 2 + i;
                      return (
                        <option key={year} value={year}>
                          {year}
                        </option>
                      );
                    })}
                  </select>
                </div>
              </div>
            </div>

            {/* File Upload Area */}
            {selectedClientId && (
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 mb-6">
                <div className="p-6 border-b border-gray-200">
                  <h2 className="text-lg font-semibold text-gray-800 flex items-center">
                    <CloudArrowUpIcon className="h-5 w-5 mr-2 text-blue-600" />
                    Payroll Document Upload
                  </h2>
                  <p className="text-gray-600 mt-1">
                    Upload payroll documents (PDF, PNG, JPG) for journal entry generation
                  </p>
                </div>

                <div className="p-6">
                  <div
                    {...getRootProps()}
                    className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
                      isDragActive
                        ? 'border-blue-400 bg-blue-50'
                        : 'border-gray-300 hover:border-gray-400'
                    }`}
                  >
                    <input {...getInputProps()} />
                    <CloudArrowUpIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    {isDragActive ? (
                      <p className="text-blue-600 font-medium">
                        Drop files here...
                      </p>
                    ) : (
                      <div>
                        <p className="text-gray-600 mb-2">
                          Drag and drop your payroll documents here, or{' '}
                          <span className="text-blue-600 font-medium cursor-pointer">
                            click to select
                          </span>
                        </p>
                        <p className="text-sm text-gray-500">
                          Supported formats: PDF, PNG, JPG (max 10MB per file)
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Files List */}
            {files.length > 0 && (
              <div className="bg-white rounded-xl shadow-lg border border-gray-200 mb-6">
                <div className="p-6 border-b border-gray-200 flex items-center justify-between">
                  <h2 className="text-lg font-semibold text-gray-800 flex items-center">
                    <DocumentDuplicateIcon className="h-5 w-5 mr-2 text-blue-600" />
                    Processed Files ({files.length})
                  </h2>

                  {files.some(f => f.status === 'completed') && (
                    <button
                      onClick={generateJournalEntries}
                      disabled={isGeneratingJournal}
                      className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {isGeneratingJournal ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2" />
                          Generating...
                        </>
                      ) : (
                        <>
                          <CalculatorIcon className="h-4 w-4 mr-2" />
                          Generate Journal Entries
                        </>
                      )}
                    </button>
                  )}
                </div>

                <div className="p-6">
                  <div className="space-y-4">
                    {files.map((file) => (
                      <div key={file.id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <div className="flex items-center">
                              <DocumentTextIcon className="h-5 w-5 text-gray-400 mr-2" />
                              <span className="font-medium text-gray-900">
                                {file.file.name}
                              </span>
                              <span className="ml-2 text-sm text-gray-500">
                                ({(file.file.size / 1024 / 1024).toFixed(2)} MB)
                              </span>
                            </div>

                            {/* Progress bars */}
                            {(file.status === 'storage-uploading' || file.status === 'processing') && (
                              <div className="mt-2 space-y-2">
                                {/* Storage upload progress */}
                                <div>
                                  <div className="flex justify-between text-xs text-gray-500 mb-1">
                                    <span>Storage Upload</span>
                                    <span>{file.storageProgress}%</span>
                                  </div>
                                  <div className="bg-gray-200 rounded-full h-2">
                                    <div
                                      className="bg-green-600 h-2 rounded-full transition-all duration-300"
                                      style={{ width: `${file.storageProgress}%` }}
                                    />
                                  </div>
                                </div>

                                {/* AI processing progress */}
                                {file.status === 'processing' && (
                                  <div>
                                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                                      <span>AI Processing</span>
                                      <span>{file.progress}%</span>
                                    </div>
                                    <div className="bg-gray-200 rounded-full h-2">
                                      <div
                                        className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                                        style={{ width: `${file.progress}%` }}
                                      />
                                    </div>
                                  </div>
                                )}
                              </div>
                            )}

                            {/* Status */}
                            <div className="mt-2 space-y-1">
                              {file.status === 'completed' && (
                                <div className="flex items-center text-green-600">
                                  <CheckCircleIcon className="h-4 w-4 mr-1" />
                                  <span className="text-sm">Successfully processed</span>
                                </div>
                              )}
                              {file.status === 'error' && (
                                <div className="flex items-center text-red-600">
                                  <ExclamationTriangleIcon className="h-4 w-4 mr-1" />
                                  <span className="text-sm">{file.error}</span>
                                </div>
                              )}
                              {file.status === 'storage-uploading' && (
                                <div className="flex items-center text-green-600">
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-green-600 mr-2" />
                                  <span className="text-sm">Uploading to storage...</span>
                                </div>
                              )}
                              {file.status === 'processing' && (
                                <div className="flex items-center text-blue-600">
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600 mr-2" />
                                  <span className="text-sm">Processing with AI...</span>
                                </div>
                              )}

                              {/* Storage status indicators */}
                              {file.storageUrl && (
                                <div className="flex items-center text-green-600">
                                  <CheckCircleIcon className="h-3 w-3 mr-1" />
                                  <span className="text-xs">Stored in Firebase</span>
                                </div>
                              )}
                              {file.storageError && (
                                <div className="flex items-center text-amber-600">
                                  <ExclamationTriangleIcon className="h-3 w-3 mr-1" />
                                  <span className="text-xs">{file.storageError}</span>
                                </div>
                              )}
                            </div>
                          </div>

                          <button
                            onClick={() => removeFile(file.id)}
                            className="ml-4 p-1 text-gray-400 hover:text-red-600 transition-colors"
                          >
                            <XMarkIcon className="h-5 w-5" />
                          </button>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Information Panel */}
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-100 rounded-xl p-6">
              <h3 className="text-lg font-semibold text-blue-800 mb-3 flex items-center">
                <InformationCircleIcon className="h-5 w-5 mr-2" />
                AI-Powered Payroll Journal Generator
              </h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 text-blue-700">
                  <p className="flex items-center">
                    <SparklesIcon className="h-4 w-4 mr-2" />
                    Automatic payroll document processing with Gemini AI
                  </p>
                  <p className="flex items-center">
                    <CurrencyDollarIcon className="h-4 w-4 mr-2" />
                    Intelligent extraction of salary data and contributions
                  </p>
                  <p className="flex items-center">
                    <CalculatorIcon className="h-4 w-4 mr-2" />
                    Automatic journal entry generation following accounting standards
                  </p>
                  <p className="flex items-center">
                    <TableCellsIcon className="h-4 w-4 mr-2" />
                    Excel export for seamless integration with accounting systems
                  </p>
                </div>
                <div className="space-y-2 text-blue-700">
                  <p className="flex items-center">
                    <CheckCircleIcon className="h-4 w-4 mr-2" />
                    Secure cloud storage in Firebase
                  </p>
                  <p className="flex items-center">
                    <UserGroupIcon className="h-4 w-4 mr-2" />
                    Employee count validation
                  </p>
                  <p className="flex items-center">
                    <DocumentTextIcon className="h-4 w-4 mr-2" />
                    Support for PDF, PNG, JPG formats
                  </p>
                  <p className="flex items-center">
                    <ClipboardDocumentListIcon className="h-4 w-4 mr-2" />
                    Comprehensive journal entry creation (BANQUE & SALAIRE)
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Journal Entries Modal */}
      <Transition appear show={showJournalModal} as={Fragment}>
        <Dialog as="div" className="relative z-[60]" onClose={() => setShowJournalModal(false)}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-6xl transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900 mb-4 flex items-center justify-between"
                  >
                    <span className="flex items-center">
                      <ClipboardDocumentListIcon className="h-6 w-6 mr-2 text-blue-600" />
                      Payroll Journal Entries
                    </span>
                    <div className="flex items-center space-x-2">
                      <button
                        onClick={exportToExcel}
                        className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center"
                      >
                        <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                        Export Excel
                      </button>
                      <button
                        onClick={() => setShowJournalModal(false)}
                        className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
                      >
                        <XMarkIcon className="h-5 w-5" />
                      </button>
                    </div>
                  </Dialog.Title>

                  {journalEntries.length > 0 && (
                    <div className="mt-4">
                      {/* Header */}
                      <div className="text-center mb-6">
                        <h2 className="text-2xl font-bold text-gray-900 mb-2">
                          PAYROLL JOURNAL ENTRIES
                        </h2>
                        <p className="text-lg text-gray-700">
                          MONTH: {selectedMonth}/{selectedYear}
                        </p>
                        <p className="text-lg text-gray-700">
                          EMPLOYER: {employerName}
                        </p>
                      </div>

                      {/* Table */}
                      <div className="overflow-x-auto">
                        <table className="min-w-full divide-y divide-gray-200 border border-gray-300">
                          <thead className="bg-gray-50">
                            <tr>
                              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Journal
                              </th>
                              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Date
                              </th>
                              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Account
                              </th>
                              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Label
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Debit
                              </th>
                              <th className="px-3 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider border-r border-gray-300">
                                Credit
                              </th>
                              <th className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Employee
                              </th>
                            </tr>
                          </thead>
                          <tbody className="bg-white divide-y divide-gray-200">
                            {journalEntries.map((entry, index) => (
                              <tr key={index} className="hover:bg-gray-50">
                                <td className="px-3 py-4 whitespace-nowrap text-sm font-medium text-gray-900 border-r border-gray-300">
                                  <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                    entry.journal === 'BANQUE'
                                      ? 'bg-blue-100 text-blue-800'
                                      : 'bg-green-100 text-green-800'
                                  }`}>
                                    {entry.journal}
                                  </span>
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 border-r border-gray-300">
                                  {entry.date}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 border-r border-gray-300">
                                  {entry.account}
                                </td>
                                <td className="px-3 py-4 text-sm text-gray-900 border-r border-gray-300">
                                  {entry.label}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-300">
                                  {entry.debit ? formatNumber(entry.debit) : '-'}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900 text-right border-r border-gray-300">
                                  {entry.credit ? formatNumber(entry.credit) : '-'}
                                </td>
                                <td className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
                                  {entry.employeeName || '-'}
                                </td>
                              </tr>
                            ))}
                          </tbody>
                        </table>
                      </div>
                    </div>
                  )}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>

      {/* File Validation Modal */}
      <Transition appear show={showValidationModal} as={Fragment}>
        <Dialog as="div" className="relative z-[70]" onClose={() => {}}>
          <Transition.Child
            as={Fragment}
            enter="ease-out duration-300"
            enterFrom="opacity-0"
            enterTo="opacity-100"
            leave="ease-in duration-200"
            leaveFrom="opacity-100"
            leaveTo="opacity-0"
          >
            <div className="fixed inset-0 bg-black bg-opacity-25" />
          </Transition.Child>

          <div className="fixed inset-0 overflow-y-auto">
            <div className="flex min-h-full items-center justify-center p-4 text-center">
              <Transition.Child
                as={Fragment}
                enter="ease-out duration-300"
                enterFrom="opacity-0 scale-95"
                enterTo="opacity-100 scale-100"
                leave="ease-in duration-200"
                leaveFrom="opacity-100 scale-100"
                leaveTo="opacity-0 scale-95"
              >
                <Dialog.Panel className="w-full max-w-md transform overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                  <Dialog.Title
                    as="h3"
                    className="text-lg font-medium leading-6 text-gray-900 mb-4 flex items-center"
                  >
                    <ExclamationTriangleIcon className="h-6 w-6 mr-2 text-amber-500" />
                    File Count Validation
                  </Dialog.Title>

                  {validationData && (
                    <div className="mt-4">
                      <p className="text-sm text-gray-600 mb-4">
                        You are uploading <strong>{validationData.uploadedCount}</strong> files,
                        but <strong>{validationData.clientName}</strong> has{' '}
                        <strong>{validationData.expectedCount}</strong> employees registered.
                      </p>
                      <p className="text-sm text-gray-600 mb-6">
                        Would you like to continue anyway or cancel to upload more files?
                      </p>

                      <div className="flex space-x-3">
                        <button
                          onClick={handleContinueAnyway}
                          className="flex-1 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                        >
                          Continue Anyway
                        </button>
                        <button
                          onClick={handleCancelUpload}
                          className="flex-1 px-4 py-2 bg-gray-300 text-gray-700 rounded-lg hover:bg-gray-400 transition-colors"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  )}
                </Dialog.Panel>
              </Transition.Child>
            </div>
          </div>
        </Dialog>
      </Transition>
    </div>
  );
};

export default ScanPaySlipJournal;